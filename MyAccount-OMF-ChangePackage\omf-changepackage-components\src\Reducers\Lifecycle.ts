import { ReducersMapObject } from "redux";
import { handleActions, Action } from "redux-actions";
import { CommonFeatures } from "bwtk";
import { Actions } from "../Actions";
import { EWidgetStatus } from "../Models";

const { actionsToComputedPropertyName } = CommonFeatures;
const {
  setWidgetStatus,
  errorOccured,
} = actionsToComputedPropertyName(Actions);
export function getWidgetBaseLifecycle(localization: any): ReducersMapObject {
  return {
    // =========== Widget state =============
    // Set localization inherited from widget props onto the store
    localization: localization.createReducer(),
    // Set widget status
    widgetStatus: handleActions<EWidgetStatus>({
      [setWidgetStatus]: (state, { payload }: Action<EWidgetStatus>) => payload
    }, EWidgetStatus.UPDATING),
    // Set any runtime errors
    error: handleActions<any | null>({
      [errorOccured]: (state, action: any) => action.payload
    }, null)
  };
}
